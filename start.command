#!/bin/bash

# MeinWort Overlay - macOS Double-Click Start Script
# This file can be double-clicked in Finder to start the app

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

echo "🚀 Starting MeinWort Overlay..."
echo "================================"

# Check if we're in the right directory
if [ ! -d "meinwort-overlay" ]; then
    echo "❌ Error: meinwort-overlay directory not found!"
    echo "Please make sure this script is in the project root directory."
    read -p "Press Enter to exit..."
    exit 1
fi

# Change to project directory
cd meinwort-overlay

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start the native desktop app
echo "🔥 Starting native desktop app..."
echo "🖥️  Opening MeinWort Overlay as desktop application"
echo "🎵 Audio visualizer will request microphone access"
echo ""
echo "Press Ctrl+C to stop the app"
echo "================================"

# Start the native Tauri app
npm run tauri dev
