#!/bin/bash

# MeinWort Overlay - Quick Start Script
# Startet den Development Server

echo "🚀 Starting MeinWort Overlay..."
echo "================================"

# Check if we're in the right directory
if [ ! -d "meinwort-overlay" ]; then
    echo "❌ Error: meinwort-overlay directory not found!"
    echo "Please run this script from the project root directory."
    exit 1
fi

# Change to project directory
cd meinwort-overlay

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start the development server
echo "🔥 Starting development server..."
echo "📱 App will be available at: http://localhost:1420/"
echo "🎵 Audio visualizer will request microphone access"
echo ""
echo "Press Ctrl+C to stop the server"
echo "================================"

npm run dev
