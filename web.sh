#!/bin/bash

# MeinWort Overlay - Web Development Script
# Startet die App im Browser (für Web-Development)

echo "🌐 Starting MeinWort Overlay in browser..."
echo "=========================================="

# Check if we're in the right directory
if [ ! -d "meinwort-overlay" ]; then
    echo "❌ Error: meinwort-overlay directory not found!"
    echo "Please run this script from the project root directory."
    exit 1
fi

# Change to project directory
cd meinwort-overlay

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start the web development server
echo "🔥 Starting web development server..."
echo "📱 App will be available at: http://localhost:1420/"
echo "🎵 Audio visualizer will request microphone access"
echo ""
echo "Press Ctrl+C to stop the server"
echo "=========================================="

npm run dev
