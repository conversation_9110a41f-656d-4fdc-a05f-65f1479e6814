#!/bin/bash

# MeinWort Overlay - Build Script
# Creates production build

echo "🏗️  Building MeinWort Overlay for production..."
echo "=============================================="

# Check if we're in the right directory
if [ ! -d "meinwort-overlay" ]; then
    echo "❌ Error: meinwort-overlay directory not found!"
    echo "Please run this script from the project root directory."
    exit 1
fi

# Change to project directory
cd meinwort-overlay

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Build the project
echo "🔨 Building project..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Build files are in: meinwort-overlay/dist/"
    echo ""
    echo "To preview the build:"
    echo "  npm run preview"
else
    echo "❌ Build failed!"
    exit 1
fi
