# MeinWort Overlay 🎵

Ein modernes Audio-reaktives Overlay mit 3D-Visualizer und intelligenten Panels.

## 🚀 Quick Start

### Option 1: Terminal
```bash
./start.sh
```

### Option 2: <PERSON><PERSON>klick (macOS)
Do<PERSON>klick auf `start.command` im Finder

### Option 3: <PERSON><PERSON><PERSON><PERSON><PERSON>
```bash
./dev.sh
```

## 📋 Verfügbare Commands

| Script | Beschreibung |
|--------|-------------|
| `./start.sh` | Startet die native Desktop-App |
| `./start.command` | macOS Doppelklick-Version (native App) |
| `./dev.sh` | Kurz-Alias für start.sh |
| `./web.sh` | Startet Web-Version im Browser |
| `./build.sh` | Erstellt Production Build |

## 🎯 Features

- **🎵 Audio-Visualizer**: Reagiert auf Mikrofon-Input
- **📱 Responsive Design**: 200x200px Compact View
- **🎨 3D Partikel**: WebGL-basierte Visualisierung
- **🔘 Smart Panels**: Document, AI+, Select+ Buttons
- **⚡ Hot Reload**: Automatische Updates während Entwicklung

## 🔧 Manuelle Installation

Falls die Scripts nicht funktionieren:

```bash
cd meinwort-overlay
npm install
npm run dev
```

## 🖥️ App-Modi

- **Native Desktop**: `./start.sh` oder `./start.command` (empfohlen)
- **Web Browser**: `./web.sh` (nur für Web-Development)
- **Production**: `./build.sh`

## 🎤 Audio-Berechtigung

Beim ersten Start wird nach Mikrofon-Berechtigung gefragt für den Audio-Visualizer.

## 🛠️ Entwicklung

- **Framework**: React + Vite + Tauri
- **Styling**: Tailwind CSS
- **3D**: Canvas 2D API mit Audio Web API
- **Audio**: Real-time Frequency Analysis

---

**Tipp**: Verwende `./start.command` für den schnellsten Start mit automatischem Browser-Öffnen! 🚀
