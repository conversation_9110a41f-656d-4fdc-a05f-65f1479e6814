{"$schema": "https://schema.tauri.app/config/2", "productName": "MeinWort Overlay", "version": "0.1.0", "identifier": "com.meinwort.overlay", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "MeinWort Overlay", "width": 200, "height": 200, "minWidth": 200, "minHeight": 200, "maxWidth": 400, "maxHeight": 600, "resizable": false, "alwaysOnTop": true, "decorations": false, "transparent": true, "skipTaskbar": true, "center": false, "x": 100, "y": 100, "visible": true, "focus": false}], "security": {"csp": null}, "macOSPrivateApi": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}