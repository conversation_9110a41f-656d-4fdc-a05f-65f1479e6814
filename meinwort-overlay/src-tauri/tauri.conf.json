{"$schema": "https://schema.tauri.app/config/2", "productName": "MeinWort Overlay", "version": "0.1.0", "identifier": "com.meinwort.overlay", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "MeinWort Overlay", "width": 200, "height": 200, "resizable": false, "alwaysOnTop": true, "decorations": true, "transparent": false, "skipTaskbar": false, "center": true, "visible": true, "focus": true}], "security": {"csp": null}, "macOSPrivateApi": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}