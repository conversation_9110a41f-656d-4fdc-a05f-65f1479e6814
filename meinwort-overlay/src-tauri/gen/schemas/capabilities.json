{"default": {"identifier": "default", "description": "Capability for the main window", "local": true, "windows": ["main"], "permissions": ["core:default", "opener:default", "core:window:allow-create", "core:window:allow-close", "core:window:allow-set-position", "core:window:allow-set-size", "core:window:allow-outer-position", "core:webview:allow-create-webview-window"]}, "panel": {"identifier": "panel", "description": "Capability for panel windows", "local": true, "windows": ["panel-*"], "permissions": ["core:default", "core:window:allow-close", "core:window:allow-set-position", "core:window:allow-set-size"]}}