cargo:rustc-check-cfg=cfg(custom_protocol)
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:dev=true
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:GLOBAL_API_SCRIPT_PATH=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.5.1/scripts/bundle.global.js
cargo:core:path__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-path-permission-files
cargo:core:event__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-event-permission-files
cargo:core:window__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-window-permission-files
cargo:core:webview__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-webview-permission-files
cargo:core:app__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-app-permission-files
cargo:core:image__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-image-permission-files
cargo:core:resources__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-resources-permission-files
cargo:core:menu__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-menu-permission-files
cargo:core:tray__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-tray-permission-files
cargo:core__CORE_PLUGIN___PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/meinwort-overlay/src-tauri/target/debug/build/tauri-58c1e4b5be568326/out/tauri-core-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
