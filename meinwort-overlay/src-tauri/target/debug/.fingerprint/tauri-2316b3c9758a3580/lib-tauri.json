{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"macos-private-api\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 13596148587733741571, "deps": [[40386456601120721, "percent_encoding", false, 7040918515472246481], [442785307232013896, "tauri_runtime", false, 12574484320858815248], [1200537532907108615, "url<PERSON><PERSON>n", false, 14422165575922037362], [1386409696764982933, "objc2", false, 2974971312524616082], [3150220818285335163, "url", false, 794408181181194199], [4143744114649553716, "raw_window_handle", false, 11673856966557422961], [4341921533227644514, "muda", false, 4238952437850398074], [4767930184903566869, "plist", false, 13044477633050448488], [4919829919303820331, "serialize_to_javascript", false, 15791584627185546124], [5986029879202738730, "log", false, 5938598544286453029], [7752760652095876438, "tauri_runtime_wry", false, 10520230497965121968], [8589231650440095114, "embed_plist", false, 1723172504503592687], [9010263965687315507, "http", false, 12058107425905163915], [9228235415475680086, "tauri_macros", false, 7665932704339306415], [9538054652646069845, "tokio", false, 16869381893929408147], [9689903380558560274, "serde", false, 11171613526217684318], [9859211262912517217, "objc2_foundation", false, 2302487150551945708], [9920160576179037441, "getrandom", false, 8867206486931345293], [10229185211513642314, "mime", false, 6605024254581884757], [10575598148575346675, "objc2_app_kit", false, 8713777048565334048], [10629569228670356391, "futures_util", false, 9469513087564136370], [10755362358622467486, "build_script_build", false, 13816169794612054968], [10806645703491011684, "thiserror", false, 2858369017034817469], [11050281405049894993, "tauri_utils", false, 15687513078402846457], [11989259058781683633, "dunce", false, 8238000382379966740], [12565293087094287914, "window_vibrancy", false, 14529720289497195238], [12986574360607194341, "serde_repr", false, 358609881111937700], [13077543566650298139, "heck", false, 6665897030291015629], [13625485746686963219, "anyhow", false, 6550937551417288592], [15367738274754116744, "serde_json", false, 10816180035799736832], [16928111194414003569, "dirs", false, 13967990436975853658], [17155886227862585100, "glob", false, 17543998344504681454]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-2316b3c9758a3580/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}