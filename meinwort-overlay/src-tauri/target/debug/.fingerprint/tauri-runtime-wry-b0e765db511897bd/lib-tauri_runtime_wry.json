{"rustc": 15497389221046826682, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 2253429996432136783, "deps": [[442785307232013896, "tauri_runtime", false, 12574484320858815248], [1386409696764982933, "objc2", false, 2974971312524616082], [3150220818285335163, "url", false, 794408181181194199], [4143744114649553716, "raw_window_handle", false, 11673856966557422961], [5986029879202738730, "log", false, 5938598544286453029], [7752760652095876438, "build_script_build", false, 11210257341927190633], [9010263965687315507, "http", false, 12058107425905163915], [9859211262912517217, "objc2_foundation", false, 2302487150551945708], [10575598148575346675, "objc2_app_kit", false, 8713777048565334048], [11050281405049894993, "tauri_utils", false, 15687513078402846457], [13223659721939363523, "tao", false, 8692670683090332021], [14794439852947137341, "wry", false, 6846992420971324211]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-b0e765db511897bd/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}