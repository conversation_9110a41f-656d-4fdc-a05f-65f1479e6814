{"rustc": 15497389221046826682, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 884757865297159968, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-07bd9cf0021ce3c7/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}