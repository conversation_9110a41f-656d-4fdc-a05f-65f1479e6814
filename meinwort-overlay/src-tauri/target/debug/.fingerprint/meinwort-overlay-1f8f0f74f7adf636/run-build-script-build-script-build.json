{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11903731913826909400, "build_script_build", false, 6756701344406750423], [10755362358622467486, "build_script_build", false, 13816169794612054968], [3935545708480822364, "build_script_build", false, 13407859424220252064]], "local": [{"RerunIfChanged": {"output": "debug/build/meinwort-overlay-1f8f0f74f7adf636/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}