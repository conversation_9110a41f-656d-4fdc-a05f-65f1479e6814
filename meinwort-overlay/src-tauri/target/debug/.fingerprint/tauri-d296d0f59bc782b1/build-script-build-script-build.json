{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"macos-private-api\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 7807529225371140086, "deps": [[283161442353679854, "tauri_build", false, 965263979756277], [11050281405049894993, "tauri_utils", false, 3355610947617948647], [13077543566650298139, "heck", false, 6665897030291015629], [17155886227862585100, "glob", false, 17543998344504681454]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-d296d0f59bc782b1/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}