@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }
  
  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: transparent;
    overflow: hidden;
  }

  #root {
    width: 100vw;
    height: 100vh;
    background: transparent;
  }
}

@layer components {
  .overlay-button {
    @apply relative flex flex-col items-center justify-center w-14 h-14 rounded-xl transition-all duration-200 ease-out;
    @apply hover:scale-105 active:scale-95 cursor-pointer;
    @apply backdrop-blur-sm border;
  }

  .overlay-button.document {
    @apply bg-gray-900/90 hover:bg-gray-800/90 border-gray-700/60;
  }

  .overlay-button.ai {
    @apply bg-orange-700/90 hover:bg-orange-600/90 border-orange-600/60;
  }

  .overlay-button.select {
    @apply bg-gray-900/90 hover:bg-gray-800/90 border-gray-700/60;
  }
  
  .overlay-panel {
    @apply absolute top-0 left-0 w-full h-full bg-dark-900/95 backdrop-blur-xl;
    @apply border border-white/10 rounded-3xl shadow-2xl;
    @apply animate-fade-in;
  }
  
  .panel-header {
    @apply flex items-center justify-between p-4 border-b border-white/10;
  }
  
  .panel-content {
    @apply flex-1 p-4 overflow-y-auto;
  }
  
  .glass-effect {
    @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl;
  }
}
