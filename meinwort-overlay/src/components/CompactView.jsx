import React from 'react';

const CompactView = ({ onButtonClick, activePanel }) => {
  const buttons = [
    {
      id: 'document',
      label: 'DOCUMENT',
      icon: '📄',
      className: 'document'
    },
    {
      id: 'ai',
      label: 'AI+',
      icon: '⭐',
      className: 'ai'
    },
    {
      id: 'select',
      label: 'SELECT+',
      icon: '⚡',
      className: 'select'
    }
  ];

  return (
    <div className="relative w-full h-full">
      {/* Buttons positioned at the bottom */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center">
        <div className="grid grid-cols-3 gap-3 w-full max-w-[160px]">
          {buttons.map((button) => (
            <div key={button.id} className="flex flex-col items-center">
              <button
                onClick={() => onButtonClick(button.id)}
                className={`overlay-button ${button.className} no-drag ${
                  activePanel === button.id ? 'ring-2 ring-primary-400 ring-opacity-60' : ''
                }`}
                title={button.label}
              >
                <span className="text-xl mb-1">{button.icon}</span>
              </button>
              <span className={`text-xs font-medium mt-1 text-center leading-tight ${
                activePanel === button.id ? 'text-primary-300' : 'text-white/70'
              }`}>
                {button.label}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CompactView;
