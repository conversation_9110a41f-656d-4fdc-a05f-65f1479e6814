import React from 'react';

const CompactView = ({ onButtonClick, activePanel }) => {
  const buttons = [
    {
      id: 'document',
      label: 'DOCUMENT',
      icon: '📄',
      className: 'document'
    },
    {
      id: 'ai',
      label: 'AI+',
      icon: '⭐',
      className: 'ai'
    },
    {
      id: 'select',
      label: 'SELECT+',
      icon: '⚡',
      className: 'select'
    }
  ];

  return (
    <div className="flex flex-col items-center justify-center w-full h-full p-4">
      <div className="grid grid-cols-3 gap-3 w-full max-w-[160px]">
        {buttons.map((button) => (
          <div key={button.id} className="flex flex-col items-center">
            <button
              onClick={() => onButtonClick(button.id)}
              className={`overlay-button ${button.className} no-drag ${
                activePanel === button.id ? 'ring-2 ring-primary-400 ring-opacity-60' : ''
              }`}
              title={button.label}
            >
              <span className="text-xl mb-1">{button.icon}</span>
            </button>
            <span className={`text-xs font-medium mt-1 text-center leading-tight ${
              activePanel === button.id ? 'text-primary-300' : 'text-white/70'
            }`}>
              {button.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CompactView;
