import React, { useState, useEffect } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import DocumentPanel from './panels/DocumentPanel';
import AIPanel from './panels/AIPanel';
import SelectPanel from './panels/SelectPanel';

const PanelApp = () => {
  const [panelType, setPanelType] = useState('document');

  useEffect(() => {
    // Get panel type from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type') || 'document';
    setPanelType(type);
  }, []);

  const handleClose = async () => {
    const window = getCurrentWindow();
    await window.close();
  };

  const getPanelTitle = () => {
    switch (panelType) {
      case 'document':
        return { title: 'Document', icon: '📄' };
      case 'ai':
        return { title: 'AI+', icon: '⭐' };
      case 'select':
        return { title: 'Select+', icon: '⚡' };
      default:
        return { title: 'Panel', icon: '📄' };
    }
  };

  const renderPanel = () => {
    switch (panelType) {
      case 'document':
        return <DocumentPanel />;
      case 'ai':
        return <AIPanel />;
      case 'select':
        return <SelectPanel />;
      default:
        return <DocumentPanel />;
    }
  };

  const { title, icon } = getPanelTitle();

  return (
    <div className="w-full h-full bg-dark-900/95 backdrop-blur-xl border border-white/10 rounded-3xl shadow-2xl overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{icon}</span>
          <h2 className="text-lg font-semibold text-white">{title}</h2>
        </div>
        
        <button
          onClick={handleClose}
          className="flex items-center justify-center w-8 h-8 rounded-lg bg-white/10 hover:bg-white/20 text-white/70 hover:text-white transition-all"
          title="Close Panel"
        >
          <span className="text-lg">×</span>
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 overflow-y-auto h-[calc(100%-80px)]">
        {renderPanel()}
      </div>
    </div>
  );
};

export default PanelApp;
