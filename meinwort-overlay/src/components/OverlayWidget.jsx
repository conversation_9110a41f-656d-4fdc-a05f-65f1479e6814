import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { getCurrentWindow, WebviewWindow } from '@tauri-apps/api/window';
import CompactView from './CompactView';

const OverlayWidget = () => {
  const [activePanel, setActivePanel] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [panelWindow, setPanelWindow] = useState(null);

  const handleButtonClick = async (panelType) => {
    try {
      const mainWindow = getCurrentWindow();
      const mainPosition = await mainWindow.outerPosition();

      if (activePanel === panelType && panelWindow) {
        // Close current panel
        await panelWindow.close();
        setPanelWindow(null);
        setActivePanel(null);
        return;
      }

      // Close existing panel if any
      if (panelWindow) {
        await panelWindow.close();
      }

      // Create new panel window next to the overlay
      const newPanelWindow = new WebviewWindow(`panel-${panelType}`, {
        url: `http://localhost:1420/panel.html?type=${panelType}`,
        title: `MeinWort ${panelType.toUpperCase()}`,
        width: 400,
        height: 600,
        x: mainPosition.x + 220, // Position next to overlay (200px + 20px gap)
        y: mainPosition.y,
        resizable: false,
        alwaysOnTop: true,
        decorations: false,
        transparent: true,
        skipTaskbar: true,
        focus: true
      });

      setPanelWindow(newPanelWindow);
      setActivePanel(panelType);

      // Listen for panel window close
      newPanelWindow.onCloseRequested(() => {
        setPanelWindow(null);
        setActivePanel(null);
      });

    } catch (error) {
      console.error('Error creating panel window:', error);
    }
  };

  // Handle dragging
  const handleMouseDown = (e) => {
    if (e.target.closest('.no-drag')) return;
    setIsDragging(true);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mouseup', handleMouseUp);
      return () => document.removeEventListener('mouseup', handleMouseUp);
    }
  }, [isDragging]);

  return (
    <div
      className="relative w-[200px] h-[200px] transition-all duration-300 ease-out"
      onMouseDown={handleMouseDown}
      style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
    >
      {/* Background with golden wave pattern */}
      <div className="absolute inset-0 rounded-3xl overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900" />
        <div className="absolute bottom-0 left-0 right-0 h-24 opacity-30">
          <svg viewBox="0 0 200 100" className="w-full h-full">
            <path
              d="M0,50 Q50,20 100,50 T200,50 L200,100 L0,100 Z"
              fill="url(#goldGradient)"
            />
            <defs>
              <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#f59332" />
                <stop offset="50%" stopColor="#f3761b" />
                <stop offset="100%" stopColor="#e45a0c" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>

      {/* Content - Always show compact view */}
      <div className="relative z-10 w-full h-full">
        <CompactView
          onButtonClick={handleButtonClick}
          activePanel={activePanel}
        />
      </div>
    </div>
  );
};

export default OverlayWidget;
