import { useState } from "react";

function App() {
  return (
    <div className="w-full h-full bg-gray-900 flex items-center justify-center">
      <div className="text-white text-center">
        <h1 className="text-xl font-bold mb-4">MeinWort Overlay</h1>
        <div className="grid grid-cols-3 gap-3">
          <button className="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-xl flex items-center justify-center text-2xl">
            📄
          </button>
          <button className="w-16 h-16 bg-orange-600 hover:bg-orange-500 rounded-xl flex items-center justify-center text-2xl">
            ⭐
          </button>
          <button className="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-xl flex items-center justify-center text-2xl">
            ⚡
          </button>
        </div>
      </div>
    </div>
  );
}

export default App;
