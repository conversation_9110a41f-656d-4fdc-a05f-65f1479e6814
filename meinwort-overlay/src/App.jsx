import { useState, useRef, useEffect } from "react";
import DocumentPanel from './components/panels/DocumentPanel';
import AIPanel from './components/panels/AIPanel';
import SelectPanel from './components/panels/SelectPanel';
import AudioVisualizer from './components/AudioVisualizer';

function App() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activePanel, setActivePanel] = useState(null);
  const canvasRef = useRef(null);

  const handleButtonClick = (panelType) => {
    if (!isExpanded) {
      // Show panel
      setIsExpanded(true);
      setActivePanel(panelType);
    } else if (activePanel === panelType) {
      // Same panel clicked - collapse
      setIsExpanded(false);
      setActivePanel(null);
    } else {
      // Different panel - just switch
      setActivePanel(panelType);
    }
  };

  const handleClose = () => {
    setIsExpanded(false);
    setActivePanel(null);
  };

  const renderPanel = () => {
    switch (activePanel) {
      case 'document':
        return <DocumentPanel />;
      case 'ai':
        return <AIPanel />;
      case 'select':
        return <SelectPanel />;
      default:
        return <DocumentPanel />;
    }
  };

  return (
    <div className="flex w-full h-full">
      {/* Compact Overlay - Always visible on the left (200x200px) */}
      <div className="w-[200px] h-[200px] flex-shrink-0">
        <div className="w-full h-full bg-black/80 rounded-l-3xl rounded-t-3xl rounded-br-none rounded-tr-none shadow-2xl border border-gray-600/20 backdrop-blur-xl">
          {/* 3D Audio Visualizer Background */}
          <div className="absolute inset-0 rounded-3xl overflow-hidden">
            <canvas
              ref={canvasRef}
              className="w-full h-full"
              style={{ background: 'transparent' }}
            />
            <AudioVisualizer canvasRef={canvasRef} />
          </div>

          {/* Content */}
          <div className="relative z-10 w-full h-full flex flex-col justify-end p-3 pb-4">
            {/* Buttons in horizontal row at bottom */}
            <div className="flex justify-center space-x-3">
              <div className="flex flex-col items-center">
                <button
                  onClick={() => handleButtonClick('document')}
                  className="w-10 h-10 bg-gray-800/90 hover:bg-gray-700 rounded-xl flex items-center justify-center text-lg transition-all backdrop-blur-sm border border-gray-600/30"
                  title="Document"
                >
                  📄
                </button>
                <span className="text-white text-[10px] font-medium mt-1 tracking-wider">DOCUMENT</span>
              </div>

              <div className="flex flex-col items-center">
                <button
                  onClick={() => handleButtonClick('ai')}
                  className="w-10 h-10 bg-orange-600/90 hover:bg-orange-500 rounded-xl flex items-center justify-center text-lg transition-all backdrop-blur-sm border border-orange-500/30"
                  title="AI+"
                >
                  ⭐
                </button>
                <span className="text-white text-[10px] font-medium mt-1 tracking-wider">AI+</span>
              </div>

              <div className="flex flex-col items-center">
                <button
                  onClick={() => handleButtonClick('select')}
                  className="w-10 h-10 bg-gray-800/90 hover:bg-gray-700 rounded-xl flex items-center justify-center text-lg transition-all backdrop-blur-sm border border-gray-600/30"
                  title="Select+"
                >
                  ⚡
                </button>
                <span className="text-white text-[10px] font-medium mt-1 tracking-wider">SELECT+</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Panel - Right side (400x400px), only visible when expanded */}
      {isExpanded && (
        <div className="w-[400px] h-full flex-shrink-0 transition-all duration-300">
          <div className="w-full h-full bg-gray-900/95 backdrop-blur-xl rounded-r-3xl rounded-t-3xl rounded-bl-none rounded-tl-none shadow-2xl border border-gray-600/30 overflow-hidden">
          {/* Header with tabs and close button */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700/50">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handleButtonClick('document')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                  activePanel === 'document'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                }`}
              >
                <span>📄</span>
                <span className="text-sm font-medium">Document</span>
              </button>
              <button
                onClick={() => handleButtonClick('ai')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                  activePanel === 'ai'
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                }`}
              >
                <span>⭐</span>
                <span className="text-sm font-medium">AI+</span>
              </button>
              <button
                onClick={() => handleButtonClick('select')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                  activePanel === 'select'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                }`}
              >
                <span>⚡</span>
                <span className="text-sm font-medium">Select+</span>
              </button>
            </div>

            <button
              onClick={handleClose}
              className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white transition-all"
              title="Close"
            >
              <span className="text-lg">×</span>
            </button>
          </div>

          {/* Panel content */}
          <div className="flex-1 p-4 overflow-y-auto h-[calc(100%-80px)]">
            {renderPanel()}
          </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
