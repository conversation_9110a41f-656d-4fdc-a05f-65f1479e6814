import { useState } from "react";
import DocumentPanel from './components/panels/DocumentPanel';
import AIPanel from './components/panels/AIPanel';
import SelectPanel from './components/panels/SelectPanel';

function App() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activePanel, setActivePanel] = useState(null);

  const handleButtonClick = (panelType) => {
    if (!isExpanded) {
      // Show panel
      setIsExpanded(true);
      setActivePanel(panelType);
    } else if (activePanel === panelType) {
      // Same panel clicked - collapse
      setIsExpanded(false);
      setActivePanel(null);
    } else {
      // Different panel - just switch
      setActivePanel(panelType);
    }
  };

  const handleClose = () => {
    setIsExpanded(false);
    setActivePanel(null);
  };

  const renderPanel = () => {
    switch (activePanel) {
      case 'document':
        return <DocumentPanel />;
      case 'ai':
        return <AIPanel />;
      case 'select':
        return <SelectPanel />;
      default:
        return <DocumentPanel />;
    }
  };

  return (
    <div className="relative w-full h-full">
      {/* Compact Overlay - Always positioned top-left, only visible when not expanded */}
      <div
        className={`absolute top-0 left-0 w-[200px] h-[200px] transition-all duration-300 ${
          isExpanded ? 'opacity-0 pointer-events-none' : 'opacity-100'
        }`}
      >
        <div className="w-full h-full bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-3xl shadow-2xl border border-gray-600/30 backdrop-blur-xl">
          {/* Golden wave background */}
          <div className="absolute bottom-0 left-0 right-0 h-16 opacity-30 rounded-b-3xl overflow-hidden">
            <svg viewBox="0 0 200 64" className="w-full h-full">
              <path
                d="M0,32 Q50,12 100,32 T200,32 L200,64 L0,64 Z"
                fill="url(#goldGradient)"
              />
              <defs>
                <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#f59332" />
                  <stop offset="50%" stopColor="#f3761b" />
                  <stop offset="100%" stopColor="#e45a0c" />
                </linearGradient>
              </defs>
            </svg>
          </div>

          {/* Content */}
          <div className="relative z-10 w-full h-full flex flex-col items-center justify-center p-4">
            <h1 className="text-white text-lg font-bold mb-4">MeinWort</h1>
            <div className="grid grid-cols-3 gap-3">
              <button
                onClick={() => handleButtonClick('document')}
                className="w-12 h-12 bg-gray-700/80 hover:bg-gray-600 rounded-xl flex items-center justify-center text-xl transition-all backdrop-blur-sm"
                title="Document"
              >
                📄
              </button>
              <button
                onClick={() => handleButtonClick('ai')}
                className="w-12 h-12 bg-orange-600/80 hover:bg-orange-500 rounded-xl flex items-center justify-center text-xl transition-all backdrop-blur-sm"
                title="AI+"
              >
                ⭐
              </button>
              <button
                onClick={() => handleButtonClick('select')}
                className="w-12 h-12 bg-gray-700/80 hover:bg-gray-600 rounded-xl flex items-center justify-center text-xl transition-all backdrop-blur-sm"
                title="Select+"
              >
                ⚡
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Panel - Full 400x600px, only visible when expanded */}
      <div
        className={`absolute inset-0 transition-all duration-300 ${
          isExpanded ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
      >
        <div className="w-full h-full bg-gray-900/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-600/30 overflow-hidden">
          {/* Header with tabs and close button */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700/50">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handleButtonClick('document')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                  activePanel === 'document'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                }`}
              >
                <span>📄</span>
                <span className="text-sm font-medium">Document</span>
              </button>
              <button
                onClick={() => handleButtonClick('ai')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                  activePanel === 'ai'
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                }`}
              >
                <span>⭐</span>
                <span className="text-sm font-medium">AI+</span>
              </button>
              <button
                onClick={() => handleButtonClick('select')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                  activePanel === 'select'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                }`}
              >
                <span>⚡</span>
                <span className="text-sm font-medium">Select+</span>
              </button>
            </div>

            <button
              onClick={handleClose}
              className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white transition-all"
              title="Close"
            >
              <span className="text-lg">×</span>
            </button>
          </div>

          {/* Panel content */}
          <div className="flex-1 p-4 overflow-y-auto h-[calc(100%-80px)]">
            {renderPanel()}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
