import { useState } from "react";
import { getCurrentWindow } from '@tauri-apps/api/window';
import DocumentPanel from './components/panels/DocumentPanel';
import AIPanel from './components/panels/AIPanel';
import SelectPanel from './components/panels/SelectPanel';

function App() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activePanel, setActivePanel] = useState(null);

  const handleButtonClick = async (panelType) => {
    try {
      const window = getCurrentWindow();

      if (!isExpanded) {
        // Expand window and show panel
        await window.setSize({ width: 400, height: 600 });
        setIsExpanded(true);
        setActivePanel(panelType);
      } else if (activePanel === panelType) {
        // Same panel clicked - collapse
        await window.setSize({ width: 200, height: 200 });
        setIsExpanded(false);
        setActivePanel(null);
      } else {
        // Different panel - just switch
        setActivePanel(panelType);
      }
    } catch (error) {
      console.error('Error resizing window:', error);
    }
  };

  const handleClose = async () => {
    try {
      const window = getCurrentWindow();
      await window.setSize({ width: 200, height: 200 });
      setIsExpanded(false);
      setActivePanel(null);
    } catch (error) {
      console.error('Error closing panel:', error);
    }
  };

  const renderPanel = () => {
    switch (activePanel) {
      case 'document':
        return <DocumentPanel />;
      case 'ai':
        return <AIPanel />;
      case 'select':
        return <SelectPanel />;
      default:
        return <DocumentPanel />;
    }
  };

  if (!isExpanded) {
    // Compact view - 200x200px with 3 buttons
    return (
      <div className="w-full h-full bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <h1 className="text-xl font-bold mb-4">MeinWort</h1>
          <div className="grid grid-cols-3 gap-3">
            <button
              onClick={() => handleButtonClick('document')}
              className="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-xl flex items-center justify-center text-2xl transition-all"
            >
              📄
            </button>
            <button
              onClick={() => handleButtonClick('ai')}
              className="w-16 h-16 bg-orange-600 hover:bg-orange-500 rounded-xl flex items-center justify-center text-2xl transition-all"
            >
              ⭐
            </button>
            <button
              onClick={() => handleButtonClick('select')}
              className="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-xl flex items-center justify-center text-2xl transition-all"
            >
              ⚡
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Expanded view - 400x600px with panel content
  return (
    <div className="w-full h-full bg-gray-900">
      {/* Header with tabs and close button */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => handleButtonClick('document')}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
              activePanel === 'document'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <span>📄</span>
            <span className="text-sm font-medium">Document</span>
          </button>
          <button
            onClick={() => handleButtonClick('ai')}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
              activePanel === 'ai'
                ? 'bg-orange-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <span>⭐</span>
            <span className="text-sm font-medium">AI+</span>
          </button>
          <button
            onClick={() => handleButtonClick('select')}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
              activePanel === 'select'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <span>⚡</span>
            <span className="text-sm font-medium">Select+</span>
          </button>
        </div>

        <button
          onClick={handleClose}
          className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white transition-all"
          title="Close"
        >
          <span className="text-lg">×</span>
        </button>
      </div>

      {/* Panel content */}
      <div className="flex-1 p-4 overflow-y-auto h-[calc(100%-80px)]">
        {renderPanel()}
      </div>
    </div>
  );
}

export default App;
