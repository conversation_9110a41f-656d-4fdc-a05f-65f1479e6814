import { useState } from "react";
import { getCurrentWindow, WebviewWindow } from '@tauri-apps/api/window';

function App() {
  const [activePanel, setActivePanel] = useState(null);
  const [panelWindow, setPanelWindow] = useState(null);

  const handleButtonClick = async (panelType) => {
    try {
      console.log(`Button clicked: ${panelType}`);

      // Close existing panel if same button clicked
      if (activePanel === panelType && panelWindow) {
        await panelWindow.close();
        setPanelWindow(null);
        setActivePanel(null);
        return;
      }

      // Close existing panel if any
      if (panelWindow) {
        await panelWindow.close();
      }

      // Get main window position
      const mainWindow = getCurrentWindow();
      const mainPosition = await mainWindow.outerPosition();

      console.log('Main window position:', mainPosition);

      // Create new panel window next to the overlay
      const newPanelWindow = new WebviewWindow(`panel-${panelType}-${Date.now()}`, {
        url: `http://localhost:1420/panel.html?type=${panelType}`,
        title: `MeinWort ${panelType.toUpperCase()}`,
        width: 400,
        height: 600,
        x: mainPosition.x + 220, // Position next to overlay (200px + 20px gap)
        y: mainPosition.y,
        resizable: false,
        alwaysOnTop: true,
        decorations: true,
        transparent: false,
        skipTaskbar: false,
        focus: true
      });

      console.log('Panel window created');
      setPanelWindow(newPanelWindow);
      setActivePanel(panelType);

      // Listen for panel window close
      newPanelWindow.onCloseRequested(() => {
        setPanelWindow(null);
        setActivePanel(null);
      });

    } catch (error) {
      console.error('Error creating panel window:', error);
    }
  };

  return (
    <div className="w-full h-full bg-gray-900 flex items-center justify-center">
      <div className="text-white text-center">
        <h1 className="text-xl font-bold mb-4">MeinWort Overlay</h1>
        <div className="grid grid-cols-3 gap-3">
          <button
            onClick={() => handleButtonClick('document')}
            className={`w-16 h-16 rounded-xl flex items-center justify-center text-2xl transition-all ${
              activePanel === 'document'
                ? 'bg-blue-600 hover:bg-blue-500 ring-2 ring-blue-400'
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            📄
          </button>
          <button
            onClick={() => handleButtonClick('ai')}
            className={`w-16 h-16 rounded-xl flex items-center justify-center text-2xl transition-all ${
              activePanel === 'ai'
                ? 'bg-orange-500 hover:bg-orange-400 ring-2 ring-orange-400'
                : 'bg-orange-600 hover:bg-orange-500'
            }`}
          >
            ⭐
          </button>
          <button
            onClick={() => handleButtonClick('select')}
            className={`w-16 h-16 rounded-xl flex items-center justify-center text-2xl transition-all ${
              activePanel === 'select'
                ? 'bg-purple-600 hover:bg-purple-500 ring-2 ring-purple-400'
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            ⚡
          </button>
        </div>
      </div>
    </div>
  );
}

export default App;
