{"version": 3, "sources": ["../../@tauri-apps/api/webview.js", "../../@tauri-apps/api/webviewWindow.js"], "sourcesContent": ["import { PhysicalPosition, PhysicalSize, Size, Position } from './dpi.js';\nimport { listen, once, emit, emitTo, TauriEvent } from './event.js';\nimport { invoke } from './core.js';\nimport { getCurrentWindow, Window } from './window.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Provides APIs to create webviews, communicate with other webviews and manipulate the current webview.\n *\n * #### Webview events\n *\n * Events can be listened to using {@link Webview.listen}:\n * ```typescript\n * import { getCurrentWebview } from \"@tauri-apps/api/webview\";\n * getCurrentWebview().listen(\"my-webview-event\", ({ event, payload }) => { });\n * ```\n *\n * @module\n */\n/**\n * Get an instance of `Webview` for the current webview.\n *\n * @since 2.0.0\n */\nfunction getCurrentWebview() {\n    return new Webview(getCurrentWindow(), window.__TAURI_INTERNALS__.metadata.currentWebview.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    });\n}\n/**\n * Gets a list of instances of `Webview` for all available webviews.\n *\n * @since 2.0.0\n */\nasync function getAllWebviews() {\n    return invoke('plugin:webview|get_all_webviews').then((webviews) => webviews.map((w) => new Webview(new Window(w.windowLabel, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    }), w.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    })));\n}\n/** @ignore */\n// events that are emitted right here instead of by the created webview\nconst localTauriEvents = ['tauri://created', 'tauri://error'];\n/**\n * Create new webview or get a handle to an existing one.\n *\n * Webviews are identified by a *label*  a unique identifier that can be used to reference it later.\n * It may only contain alphanumeric characters `a-zA-Z` plus the following special characters `-`, `/`, `:` and `_`.\n *\n * @example\n * ```typescript\n * import { Window } from \"@tauri-apps/api/window\"\n * import { Webview } from \"@tauri-apps/api/webview\"\n *\n * const appWindow = new Window('uniqueLabel');\n *\n * // loading embedded asset:\n * const webview = new Webview(appWindow, 'theUniqueLabel', {\n *   url: 'path/to/page.html'\n * });\n * // alternatively, load a remote URL:\n * const webview = new Webview(appWindow, 'theUniqueLabel', {\n *   url: 'https://github.com/tauri-apps/tauri'\n * });\n *\n * webview.once('tauri://created', function () {\n *  // webview successfully created\n * });\n * webview.once('tauri://error', function (e) {\n *  // an error happened creating the webview\n * });\n *\n * // emit an event to the backend\n * await webview.emit(\"some-event\", \"data\");\n * // listen to an event from the backend\n * const unlisten = await webview.listen(\"event-name\", e => {});\n * unlisten();\n * ```\n *\n * @since 2.0.0\n */\nclass Webview {\n    /**\n     * Creates a new Webview.\n     * @example\n     * ```typescript\n     * import { Window } from '@tauri-apps/api/window'\n     * import { Webview } from '@tauri-apps/api/webview'\n     * const appWindow = new Window('my-label')\n     * const webview = new Webview(appWindow, 'my-label', {\n     *   url: 'https://github.com/tauri-apps/tauri'\n     * });\n     * webview.once('tauri://created', function () {\n     *  // webview successfully created\n     * });\n     * webview.once('tauri://error', function (e) {\n     *  // an error happened creating the webview\n     * });\n     * ```\n     *\n     * @param window the window to add this webview to.\n     * @param label The unique webview label. Must be alphanumeric: `a-zA-Z-/:_`.\n     * @returns The {@link Webview} instance to communicate with the webview.\n     */\n    constructor(window, label, options) {\n        this.window = window;\n        this.label = label;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        this.listeners = Object.create(null);\n        // @ts-expect-error `skip` is not a public API so it is not defined in WebviewOptions\n        if (!(options === null || options === void 0 ? void 0 : options.skip)) {\n            invoke('plugin:webview|create_webview', {\n                windowLabel: window.label,\n                label,\n                options\n            })\n                .then(async () => this.emit('tauri://created'))\n                .catch(async (e) => this.emit('tauri://error', e));\n        }\n    }\n    /**\n     * Gets the Webview for the webview associated with the given label.\n     * @example\n     * ```typescript\n     * import { Webview } from '@tauri-apps/api/webview';\n     * const mainWebview = Webview.getByLabel('main');\n     * ```\n     *\n     * @param label The webview label.\n     * @returns The Webview instance to communicate with the webview or null if the webview doesn't exist.\n     */\n    static async getByLabel(label) {\n        var _a;\n        return (_a = (await getAllWebviews()).find((w) => w.label === label)) !== null && _a !== void 0 ? _a : null;\n    }\n    /**\n     * Get an instance of `Webview` for the current webview.\n     */\n    static getCurrent() {\n        return getCurrentWebview();\n    }\n    /**\n     * Gets a list of instances of `Webview` for all available webviews.\n     */\n    static async getAll() {\n        return getAllWebviews();\n    }\n    /**\n     * Listen to an emitted event on this webview.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const unlisten = await getCurrentWebview().listen<string>('state-changed', (event) => {\n     *   console.log(`Got error: ${payload}`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async listen(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return listen(event, handler, {\n            target: { kind: 'Webview', label: this.label }\n        });\n    }\n    /**\n     * Listen to an emitted event on this webview only once.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const unlisten = await getCurrent().once<null>('initialized', (event) => {\n     *   console.log(`Webview initialized!`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async once(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return once(event, handler, {\n            target: { kind: 'Webview', label: this.label }\n        });\n    }\n    /**\n     * Emits an event to all {@link EventTarget|targets}.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().emit('webview-loaded', { loggedIn: true, token: 'authToken' });\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param payload Event payload.\n     */\n    async emit(event, payload) {\n        if (localTauriEvents.includes(event)) {\n            // eslint-disable-next-line\n            for (const handler of this.listeners[event] || []) {\n                handler({\n                    event,\n                    id: -1,\n                    payload\n                });\n            }\n            return;\n        }\n        return emit(event, payload);\n    }\n    /**\n     * Emits an event to all {@link EventTarget|targets} matching the given target.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().emitTo('main', 'webview-loaded', { loggedIn: true, token: 'authToken' });\n     * ```\n     *\n     * @param target Label of the target Window/Webview/WebviewWindow or raw {@link EventTarget} object.\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param payload Event payload.\n     */\n    async emitTo(target, event, payload) {\n        if (localTauriEvents.includes(event)) {\n            // eslint-disable-next-line\n            for (const handler of this.listeners[event] || []) {\n                handler({\n                    event,\n                    id: -1,\n                    payload\n                });\n            }\n            return;\n        }\n        return emitTo(target, event, payload);\n    }\n    /** @ignore */\n    _handleTauriEvent(event, handler) {\n        if (localTauriEvents.includes(event)) {\n            if (!(event in this.listeners)) {\n                // eslint-disable-next-line security/detect-object-injection\n                this.listeners[event] = [handler];\n            }\n            else {\n                // eslint-disable-next-line security/detect-object-injection\n                this.listeners[event].push(handler);\n            }\n            return true;\n        }\n        return false;\n    }\n    // Getters\n    /**\n     * The position of the top-left hand corner of the webview's client area relative to the top-left hand corner of the desktop.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const position = await getCurrentWebview().position();\n     * ```\n     *\n     * @returns The webview's position.\n     */\n    async position() {\n        return invoke('plugin:webview|webview_position', {\n            label: this.label\n        }).then((p) => new PhysicalPosition(p));\n    }\n    /**\n     * The physical size of the webview's client area.\n     * The client area is the content of the webview, excluding the title bar and borders.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const size = await getCurrentWebview().size();\n     * ```\n     *\n     * @returns The webview's size.\n     */\n    async size() {\n        return invoke('plugin:webview|webview_size', {\n            label: this.label\n        }).then((s) => new PhysicalSize(s));\n    }\n    // Setters\n    /**\n     * Closes the webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().close();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async close() {\n        return invoke('plugin:webview|webview_close', {\n            label: this.label\n        });\n    }\n    /**\n     * Resizes the webview.\n     * @example\n     * ```typescript\n     * import { getCurrent, LogicalSize } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setSize(new LogicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical size.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setSize(size) {\n        return invoke('plugin:webview|set_webview_size', {\n            label: this.label,\n            value: size instanceof Size ? size : new Size(size)\n        });\n    }\n    /**\n     * Sets the webview position.\n     * @example\n     * ```typescript\n     * import { getCurrent, LogicalPosition } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setPosition(new LogicalPosition(600, 500));\n     * ```\n     *\n     * @param position The new position, in logical or physical pixels.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setPosition(position) {\n        return invoke('plugin:webview|set_webview_position', {\n            label: this.label,\n            value: position instanceof Position ? position : new Position(position)\n        });\n    }\n    /**\n     * Bring the webview to front and focus.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setFocus();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setFocus() {\n        return invoke('plugin:webview|set_webview_focus', {\n            label: this.label\n        });\n    }\n    /**\n     * Hide the webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().hide();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async hide() {\n        return invoke('plugin:webview|webview_hide', {\n            label: this.label\n        });\n    }\n    /**\n     * Show the webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().show();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async show() {\n        return invoke('plugin:webview|webview_show', {\n            label: this.label\n        });\n    }\n    /**\n     * Set webview zoom level.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setZoom(1.5);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setZoom(scaleFactor) {\n        return invoke('plugin:webview|set_webview_zoom', {\n            label: this.label,\n            value: scaleFactor\n        });\n    }\n    /**\n     * Moves this webview to the given label.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().reparent('other-window');\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async reparent(window) {\n        return invoke('plugin:webview|reparent', {\n            label: this.label,\n            window: typeof window === 'string' ? window : window.label\n        });\n    }\n    /**\n     * Clears all browsing data for this webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().clearAllBrowsingData();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async clearAllBrowsingData() {\n        return invoke('plugin:webview|clear_all_browsing_data');\n    }\n    /**\n     * Specify the webview background color.\n     *\n     * #### Platfrom-specific:\n     *\n     * - **macOS / iOS**: Not implemented.\n     * - **Windows**:\n     *   - On Windows 7, transparency is not supported and the alpha value will be ignored.\n     *   - On Windows higher than 7: translucent colors are not supported so any alpha value other than `0` will be replaced by `255`\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.1.0\n     */\n    async setBackgroundColor(color) {\n        return invoke('plugin:webview|set_webview_background_color', { color });\n    }\n    // Listeners\n    /**\n     * Listen to a file drop event.\n     * The listener is triggered when the user hovers the selected files on the webview,\n     * drops the files or cancels the operation.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from \"@tauri-apps/api/webview\";\n     * const unlisten = await getCurrentWebview().onDragDropEvent((event) => {\n     *  if (event.payload.type === 'over') {\n     *    console.log('User hovering', event.payload.position);\n     *  } else if (event.payload.type === 'drop') {\n     *    console.log('User dropped', event.payload.paths);\n     *  } else {\n     *    console.log('File drop cancelled');\n     *  }\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * When the debugger panel is open, the drop position of this event may be inaccurate due to a known limitation.\n     * To retrieve the correct drop position, please detach the debugger.\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onDragDropEvent(handler) {\n        const unlistenDragEnter = await this.listen(TauriEvent.DRAG_ENTER, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'enter',\n                    paths: event.payload.paths,\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDragOver = await this.listen(TauriEvent.DRAG_OVER, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'over',\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDragDrop = await this.listen(TauriEvent.DRAG_DROP, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'drop',\n                    paths: event.payload.paths,\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDragLeave = await this.listen(TauriEvent.DRAG_LEAVE, (event) => {\n            handler({ ...event, payload: { type: 'leave' } });\n        });\n        return () => {\n            unlistenDragEnter();\n            unlistenDragDrop();\n            unlistenDragOver();\n            unlistenDragLeave();\n        };\n    }\n}\n\nexport { Webview, getAllWebviews, getCurrentWebview };\n", "import { getCurrentWebview, Webview } from './webview.js';\nimport { Window } from './window.js';\nimport { listen, once } from './event.js';\nimport { invoke } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Get an instance of `Webview` for the current webview window.\n *\n * @since 2.0.0\n */\nfunction getCurrentWebviewWindow() {\n    const webview = getCurrentWebview();\n    // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n    return new WebviewWindow(webview.label, { skip: true });\n}\n/**\n * Gets a list of instances of `Webview` for all available webview windows.\n *\n * @since 2.0.0\n */\nasync function getAllWebviewWindows() {\n    return invoke('plugin:window|get_all_windows').then((windows) => windows.map((w) => new WebviewWindow(w, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    })));\n}\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nclass WebviewWindow {\n    /**\n     * Creates a new {@link Window} hosting a {@link Webview}.\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/webviewWindow'\n     * const webview = new WebviewWindow('my-label', {\n     *   url: 'https://github.com/tauri-apps/tauri'\n     * });\n     * webview.once('tauri://created', function () {\n     *  // webview successfully created\n     * });\n     * webview.once('tauri://error', function (e) {\n     *  // an error happened creating the webview\n     * });\n     * ```\n     *\n     * @param label The unique webview label. Must be alphanumeric: `a-zA-Z-/:_`.\n     * @returns The {@link WebviewWindow} instance to communicate with the window and webview.\n     */\n    constructor(label, options = {}) {\n        var _a;\n        this.label = label;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        this.listeners = Object.create(null);\n        // @ts-expect-error `skip` is not a public API so it is not defined in WebviewOptions\n        if (!(options === null || options === void 0 ? void 0 : options.skip)) {\n            invoke('plugin:webview|create_webview_window', {\n                options: {\n                    ...options,\n                    parent: typeof options.parent === 'string'\n                        ? options.parent\n                        : (_a = options.parent) === null || _a === void 0 ? void 0 : _a.label,\n                    label\n                }\n            })\n                .then(async () => this.emit('tauri://created'))\n                .catch(async (e) => this.emit('tauri://error', e));\n        }\n    }\n    /**\n     * Gets the Webview for the webview associated with the given label.\n     * @example\n     * ```typescript\n     * import { Webview } from '@tauri-apps/api/webviewWindow';\n     * const mainWebview = Webview.getByLabel('main');\n     * ```\n     *\n     * @param label The webview label.\n     * @returns The Webview instance to communicate with the webview or null if the webview doesn't exist.\n     */\n    static async getByLabel(label) {\n        var _a;\n        const webview = (_a = (await getAllWebviewWindows()).find((w) => w.label === label)) !== null && _a !== void 0 ? _a : null;\n        if (webview) {\n            // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n            return new WebviewWindow(webview.label, { skip: true });\n        }\n        return null;\n    }\n    /**\n     * Get an instance of `Webview` for the current webview.\n     */\n    static getCurrent() {\n        return getCurrentWebviewWindow();\n    }\n    /**\n     * Gets a list of instances of `Webview` for all available webviews.\n     */\n    static async getAll() {\n        return getAllWebviewWindows();\n    }\n    /**\n     * Listen to an emitted event on this webivew window.\n     *\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/webviewWindow';\n     * const unlisten = await WebviewWindow.getCurrent().listen<string>('state-changed', (event) => {\n     *   console.log(`Got error: ${payload}`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async listen(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return listen(event, handler, {\n            target: { kind: 'WebviewWindow', label: this.label }\n        });\n    }\n    /**\n     * Listen to an emitted event on this webview window only once.\n     *\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/webviewWindow';\n     * const unlisten = await WebviewWindow.getCurrent().once<null>('initialized', (event) => {\n     *   console.log(`Webview initialized!`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async once(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return once(event, handler, {\n            target: { kind: 'WebviewWindow', label: this.label }\n        });\n    }\n    /**\n     * Set the window and webview background color.\n     *\n     * #### Platform-specific:\n     *\n     * - **Android / iOS:** Unsupported for the window layer.\n     * - **macOS / iOS**: Not implemented for the webview layer.\n     * - **Windows**:\n     *   - alpha channel is ignored for the window layer.\n     *   - On Windows 7, alpha channel is ignored for the webview layer.\n     *   - On Windows 8 and newer, if alpha channel is not `0`, it will be ignored.\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.1.0\n     */\n    async setBackgroundColor(color) {\n        return invoke('plugin:window|set_background_color', { color }).then(() => {\n            return invoke('plugin:webview|set_webview_background_color', { color });\n        });\n    }\n}\n// Order matters, we use window APIs by default\napplyMixins(WebviewWindow, [Window, Webview]);\n/** Extends a base class by other specified classes, without overriding existing properties */\nfunction applyMixins(baseClass, extendedClasses) {\n    (Array.isArray(extendedClasses)\n        ? extendedClasses\n        : [extendedClasses]).forEach((extendedClass) => {\n        Object.getOwnPropertyNames(extendedClass.prototype).forEach((name) => {\n            var _a;\n            if (typeof baseClass.prototype === 'object'\n                && baseClass.prototype\n                && name in baseClass.prototype)\n                return;\n            Object.defineProperty(baseClass.prototype, name, \n            // eslint-disable-next-line\n            (_a = Object.getOwnPropertyDescriptor(extendedClass.prototype, name)) !== null && _a !== void 0 ? _a : Object.create(null));\n        });\n    });\n}\n\nexport { WebviewWindow, getAllWebviewWindows, getCurrentWebviewWindow };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA0BA,SAAS,oBAAoB;AACzB,SAAO,IAAI,QAAQ,iBAAiB,GAAG,OAAO,oBAAoB,SAAS,eAAe,OAAO;AAAA;AAAA,IAE7F,MAAM;AAAA,EACV,CAAC;AACL;AAMA,eAAe,iBAAiB;AAC5B,SAAO,OAAO,iCAAiC,EAAE,KAAK,CAAC,aAAa,SAAS,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI,OAAO,EAAE,aAAa;AAAA;AAAA,IAE1H,MAAM;AAAA,EACV,CAAC,GAAG,EAAE,OAAO;AAAA;AAAA,IAET,MAAM;AAAA,EACV,CAAC,CAAC,CAAC;AACP;AAGA,IAAM,mBAAmB,CAAC,mBAAmB,eAAe;AAuC5D,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBV,YAAYA,SAAQ,OAAO,SAAS;AAChC,SAAK,SAASA;AACd,SAAK,QAAQ;AAEb,SAAK,YAAY,uBAAO,OAAO,IAAI;AAEnC,QAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACnE,aAAO,iCAAiC;AAAA,QACpC,aAAaA,QAAO;AAAA,QACpB;AAAA,QACA;AAAA,MACJ,CAAC,EACI,KAAK,YAAY,KAAK,KAAK,iBAAiB,CAAC,EAC7C,MAAM,OAAO,MAAM,KAAK,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,WAAW,OAAO;AAC3B,QAAI;AACJ,YAAQ,MAAM,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,EAAE,UAAU,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC3G;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,aAAa;AAChB,WAAO,kBAAkB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS;AAClB,WAAO,eAAe;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,OAAO,OAAO,SAAS;AACzB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC1B,QAAQ,EAAE,MAAM,WAAW,OAAO,KAAK,MAAM;AAAA,IACjD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,SAAS;AAAA,MACxB,QAAQ,EAAE,MAAM,WAAW,OAAO,KAAK,MAAM;AAAA,IACjD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAElC,iBAAW,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,GAAG;AAC/C,gBAAQ;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,OAAO,QAAQ,OAAO,SAAS;AACjC,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAElC,iBAAW,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,GAAG;AAC/C,gBAAQ;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,WAAO,OAAO,QAAQ,OAAO,OAAO;AAAA,EACxC;AAAA;AAAA,EAEA,kBAAkB,OAAO,SAAS;AAC9B,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAClC,UAAI,EAAE,SAAS,KAAK,YAAY;AAE5B,aAAK,UAAU,KAAK,IAAI,CAAC,OAAO;AAAA,MACpC,OACK;AAED,aAAK,UAAU,KAAK,EAAE,KAAK,OAAO;AAAA,MACtC;AACA,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,WAAW;AACb,WAAO,OAAO,mCAAmC;AAAA,MAC7C,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,iBAAiB,CAAC,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,OAAO;AACT,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,QAAQ;AACV,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,QAAQ,MAAM;AAChB,WAAO,OAAO,mCAAmC;AAAA,MAC7C,OAAO,KAAK;AAAA,MACZ,OAAO,gBAAgB,OAAO,OAAO,IAAI,KAAK,IAAI;AAAA,IACtD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,UAAU;AACxB,WAAO,OAAO,uCAAuC;AAAA,MACjD,OAAO,KAAK;AAAA,MACZ,OAAO,oBAAoB,WAAW,WAAW,IAAI,SAAS,QAAQ;AAAA,IAC1E,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,OAAO,oCAAoC;AAAA,MAC9C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,QAAQ,aAAa;AACvB,WAAO,OAAO,mCAAmC;AAAA,MAC7C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,SAASA,SAAQ;AACnB,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,KAAK;AAAA,MACZ,QAAQ,OAAOA,YAAW,WAAWA,UAASA,QAAO;AAAA,IACzD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,uBAAuB;AACzB,WAAO,OAAO,wCAAwC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,mBAAmB,OAAO;AAC5B,WAAO,OAAO,+CAA+C,EAAE,MAAM,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA,MAAM,gBAAgB,SAAS;AAC3B,UAAM,oBAAoB,MAAM,KAAK,OAAO,WAAW,YAAY,CAAC,UAAU;AAC1E,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,QAAQ;AAAA,UACrB,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,mBAAmB,MAAM,KAAK,OAAO,WAAW,WAAW,CAAC,UAAU;AACxE,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,mBAAmB,MAAM,KAAK,OAAO,WAAW,WAAW,CAAC,UAAU;AACxE,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,QAAQ;AAAA,UACrB,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,oBAAoB,MAAM,KAAK,OAAO,WAAW,YAAY,CAAC,UAAU;AAC1E,cAAQ,EAAE,GAAG,OAAO,SAAS,EAAE,MAAM,QAAQ,EAAE,CAAC;AAAA,IACpD,CAAC;AACD,WAAO,MAAM;AACT,wBAAkB;AAClB,uBAAiB;AACjB,uBAAiB;AACjB,wBAAkB;AAAA,IACtB;AAAA,EACJ;AACJ;;;AChhBA,SAAS,0BAA0B;AAC/B,QAAM,UAAU,kBAAkB;AAElC,SAAO,IAAI,cAAc,QAAQ,OAAO,EAAE,MAAM,KAAK,CAAC;AAC1D;AAMA,eAAe,uBAAuB;AAClC,SAAO,OAAO,+BAA+B,EAAE,KAAK,CAAC,YAAY,QAAQ,IAAI,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA;AAAA,IAErG,MAAM;AAAA,EACV,CAAC,CAAC,CAAC;AACP;AAEA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBhB,YAAY,OAAO,UAAU,CAAC,GAAG;AAC7B,QAAI;AACJ,SAAK,QAAQ;AAEb,SAAK,YAAY,uBAAO,OAAO,IAAI;AAEnC,QAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACnE,aAAO,wCAAwC;AAAA,QAC3C,SAAS;AAAA,UACL,GAAG;AAAA,UACH,QAAQ,OAAO,QAAQ,WAAW,WAC5B,QAAQ,UACP,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,UACpE;AAAA,QACJ;AAAA,MACJ,CAAC,EACI,KAAK,YAAY,KAAK,KAAK,iBAAiB,CAAC,EAC7C,MAAM,OAAO,MAAM,KAAK,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,WAAW,OAAO;AAC3B,QAAI;AACJ,UAAM,WAAW,MAAM,MAAM,qBAAqB,GAAG,KAAK,CAAC,MAAM,EAAE,UAAU,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK;AACtH,QAAI,SAAS;AAET,aAAO,IAAI,eAAc,QAAQ,OAAO,EAAE,MAAM,KAAK,CAAC;AAAA,IAC1D;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,aAAa;AAChB,WAAO,wBAAwB;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS;AAClB,WAAO,qBAAqB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,OAAO,OAAO,SAAS;AACzB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC1B,QAAQ,EAAE,MAAM,iBAAiB,OAAO,KAAK,MAAM;AAAA,IACvD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,SAAS;AAAA,MACxB,QAAQ,EAAE,MAAM,iBAAiB,OAAO,KAAK,MAAM;AAAA,IACvD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,mBAAmB,OAAO;AAC5B,WAAO,OAAO,sCAAsC,EAAE,MAAM,CAAC,EAAE,KAAK,MAAM;AACtE,aAAO,OAAO,+CAA+C,EAAE,MAAM,CAAC;AAAA,IAC1E,CAAC;AAAA,EACL;AACJ;AAEA,YAAY,eAAe,CAAC,QAAQ,OAAO,CAAC;AAE5C,SAAS,YAAY,WAAW,iBAAiB;AAC7C,GAAC,MAAM,QAAQ,eAAe,IACxB,kBACA,CAAC,eAAe,GAAG,QAAQ,CAAC,kBAAkB;AAChD,WAAO,oBAAoB,cAAc,SAAS,EAAE,QAAQ,CAAC,SAAS;AAClE,UAAI;AACJ,UAAI,OAAO,UAAU,cAAc,YAC5B,UAAU,aACV,QAAQ,UAAU;AACrB;AACJ,aAAO;AAAA,QAAe,UAAU;AAAA,QAAW;AAAA;AAAA,SAE1C,KAAK,OAAO,yBAAyB,cAAc,WAAW,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,uBAAO,OAAO,IAAI;AAAA,MAAC;AAAA,IAC9H,CAAC;AAAA,EACL,CAAC;AACL;", "names": ["window"]}