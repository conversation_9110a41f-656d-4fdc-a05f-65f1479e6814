{"name": "@tauri-apps/api", "version": "2.5.0", "description": "Tauri API definitions", "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "repository": {"type": "git", "url": "git+https://github.com/tauri-apps/tauri.git"}, "contributors": ["Tauri Programme within The Commons Conservancy"], "license": "Apache-2.0 OR MIT", "bugs": {"url": "https://github.com/tauri-apps/tauri/issues"}, "homepage": "https://github.com/tauri-apps/tauri#readme", "type": "module", "main": "./index.cjs", "module": "./index.js", "types": "./index.d.ts", "exports": {".": {"import": "./index.js", "require": "./index.cjs", "types": "./index.d.ts"}, "./*": {"import": "./*.js", "require": "./*.cjs", "types": "./*.d.ts"}, "./package.json": "./package.json"}, "devDependencies": {"@eslint/js": "^9.21.0", "@rollup/plugin-terser": "0.4.4", "@rollup/plugin-typescript": "12.1.2", "@types/eslint": "^9.6.1", "@types/node": "^22.13.5", "eslint": "^9.21.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-security": "3.0.1", "fast-glob": "3.3.3", "globals": "^16.0.0", "rollup": "4.40.0", "tslib": "^2.8.1", "typescript": "^5.8.3", "typescript-eslint": "^8.25.0"}, "scripts": {"build": "rollup -c --configPlugin typescript", "build:debug": "rollup -c --configPlugin typescript", "npm-pack": "pnpm build && cd ./dist && npm pack", "npm-publish": "pnpm build && cd ./dist && pnpm publish --access public --loglevel silly --no-git-checks", "ts:check": "tsc --noEmit", "eslint:check": "eslint src/**/*.ts", "eslint:fix": "eslint src/**/*.ts --fix"}}